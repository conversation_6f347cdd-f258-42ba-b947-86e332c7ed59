#!/usr/bin/env python3
"""
简单的模拟API服务器，用于测试任务接口
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse
import re
from datetime import datetime

class MockAPIHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理GET请求"""
        path = urllib.parse.urlparse(self.path).path
        query = urllib.parse.parse_qs(urllib.parse.urlparse(self.path).query)
        
        if path == '/api/v1/tasks':
            # 获取所有任务或按状态过滤
            status_filter = query.get('status', [None])[0]
            tasks = self.get_mock_tasks(status_filter)
            self.send_json_response(200, {"tasks": tasks, "total": len(tasks)})
            
        elif re.match(r'/api/v1/tasks/(\d+)$', path):
            # 按ID获取任务
            task_id = path.split('/')[-1]
            task = self.get_mock_task_by_id(task_id)
            if task:
                self.send_json_response(200, task)
            else:
                self.send_json_response(404, {"error": "Task not found"})
                
        else:
            self.send_json_response(404, {"error": "Not found"})
    
    def do_POST(self):
        """处理POST请求"""
        path = urllib.parse.urlparse(self.path).path
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length).decode('utf-8')
        
        try:
            data = json.loads(post_data) if post_data else {}
        except json.JSONDecodeError:
            self.send_json_response(400, {"error": "Invalid JSON"})
            return
        
        if path == '/api/v1/tasks':
            # 创建任务
            response = self.create_mock_task(data)
            self.send_json_response(response['status'], response['data'])
            
        elif path == '/api/v1/classes':
            # 创建班级
            response = self.create_mock_class(data)
            self.send_json_response(response['status'], response['data'])
            
        elif re.match(r'/api/v1/tasks/(\d+)/submissions$', path):
            # 提交任务
            task_id = path.split('/')[-2]
            response = self.create_mock_submission(task_id, data)
            self.send_json_response(response['status'], response['data'])
            
        elif re.match(r'/api/v1/submissions/(\d+)/grade$', path):
            # 评分提交
            submission_id = path.split('/')[-2]
            response = self.grade_mock_submission(submission_id, data)
            self.send_json_response(response['status'], response['data'])
            
        else:
            self.send_json_response(404, {"error": "Not found"})
    
    def do_PUT(self):
        """处理PUT请求"""
        path = urllib.parse.urlparse(self.path).path
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length).decode('utf-8')
        
        try:
            data = json.loads(post_data) if post_data else {}
        except json.JSONDecodeError:
            self.send_json_response(400, {"error": "Invalid JSON"})
            return
        
        if re.match(r'/api/v1/tasks/(\d+)$', path):
            # 更新任务
            task_id = path.split('/')[-1]
            response = self.update_mock_task(task_id, data)
            self.send_json_response(response['status'], response['data'])
        else:
            self.send_json_response(404, {"error": "Not found"})
    
    def do_DELETE(self):
        """处理DELETE请求"""
        path = urllib.parse.urlparse(self.path).path
        
        if re.match(r'/api/v1/tasks/(\d+)$', path):
            # 删除任务
            task_id = path.split('/')[-1]
            self.send_json_response(204, None)
        else:
            self.send_json_response(404, {"error": "Not found"})
    
    def send_json_response(self, status_code, data):
        """发送JSON响应"""
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        if data is not None:
            response = json.dumps(data, ensure_ascii=False, indent=2)
            self.wfile.write(response.encode('utf-8'))
    
    def get_mock_tasks(self, status_filter=None):
        """获取模拟任务列表"""
        tasks = [
            {
                "id": 1,
                "title": "单词听写测试",
                "task_type": "dictation",
                "subject": "english",
                "status": "published",
                "teacher_id": "teacher001",
                "class_id": "class001",
                "created_at": "2024-01-01T10:00:00Z",
                "deadline": "2024-12-31T23:59:59Z"
            },
            {
                "id": 2,
                "title": "句子跟读练习",
                "task_type": "sentence_repeat",
                "subject": "english", 
                "status": "draft",
                "teacher_id": "teacher001",
                "class_id": "class001",
                "created_at": "2024-01-02T10:00:00Z"
            }
        ]
        
        if status_filter:
            tasks = [t for t in tasks if t['status'] == status_filter]
        
        return tasks
    
    def get_mock_task_by_id(self, task_id):
        """根据ID获取模拟任务"""
        tasks = self.get_mock_tasks()
        for task in tasks:
            if str(task['id']) == str(task_id):
                task['contents'] = [
                    {
                        "id": 1,
                        "content_type": task['task_type'],
                        "points": 100,
                        "order_num": 1,
                        "selected_word_ids": [1, 2, 3] if task['task_type'] == 'dictation' else None,
                        "selected_sentence_ids": [1, 2, 3] if task['task_type'] == 'sentence_repeat' else None,
                        "generate_mode": "manual"
                    }
                ]
                return task
        return None
    
    def create_mock_task(self, data):
        """创建模拟任务"""
        # 验证必填字段
        required_fields = ['title', 'task_type', 'subject', 'teacher_id', 'class_id', 'contents']
        for field in required_fields:
            if field not in data:
                return {"status": 400, "data": {"error": f"Missing required field: {field}"}}
        
        # 验证标题长度
        if len(data.get('title', '')) > 100:
            return {"status": 400, "data": {"error": "Title too long (max 100 characters)"}}
        
        # 验证任务类型
        valid_types = ['dictation', 'sentence_repeat', 'spelling', 'quiz']
        if data.get('task_type') not in valid_types:
            return {"status": 400, "data": {"error": f"Invalid task_type. Must be one of: {valid_types}"}}
        
        # 验证已发布任务必须有截止时间
        if data.get('status') == 'published' and not data.get('deadline'):
            return {"status": 400, "data": {"error": "Published tasks must have a deadline"}}
        
        # 验证内容分值
        contents = data.get('contents', [])
        total_points = sum(content.get('points', 0) for content in contents)
        if total_points != 100:
            return {"status": 400, "data": {"error": f"Total points must equal 100, got {total_points}"}}
        
        # 验证任务类型和内容类型匹配
        task_type = data.get('task_type')
        for content in contents:
            content_type = content.get('content_type')
            if task_type == 'dictation' and content_type != 'dictation':
                return {"status": 400, "data": {"error": "Task type and content type mismatch"}}
            elif task_type == 'sentence_repeat' and content_type != 'sentence_repeat':
                return {"status": 400, "data": {"error": "Task type and content type mismatch"}}
        
        # 创建成功响应
        task = {
            "id": 123,  # 模拟生成的ID
            "title": data['title'],
            "task_type": data['task_type'],
            "subject": data['subject'],
            "status": data.get('status', 'draft'),
            "teacher_id": data['teacher_id'],
            "class_id": data['class_id'],
            "deadline": data.get('deadline'),
            "created_at": datetime.now().isoformat() + "Z",
            "contents": contents
        }
        
        return {"status": 201, "data": task}
    
    def create_mock_class(self, data):
        """创建模拟班级"""
        if not data.get('name'):
            return {"status": 400, "data": {"error": "Missing required field: name"}}
        
        class_obj = {
            "id": data.get('id', 'class_123'),
            "name": data['name'],
            "description": data.get('description', ''),
            "created_at": datetime.now().isoformat() + "Z"
        }
        
        return {"status": 201, "data": class_obj}
    
    def create_mock_submission(self, task_id, data):
        """创建模拟提交"""
        submission = {
            "id": 456,  # 模拟生成的ID
            "task_id": int(task_id),
            "content_id": data.get('content_id', 1),
            "response": data.get('response', ''),
            "media_files": data.get('media_files', []),
            "submitted_at": datetime.now().isoformat() + "Z",
            "status": "submitted"
        }
        
        return {"status": 201, "data": submission}
    
    def grade_mock_submission(self, submission_id, data):
        """评分模拟提交"""
        grade = {
            "submission_id": int(submission_id),
            "score": data.get('score', 0),
            "feedback": data.get('feedback', ''),
            "graded_at": datetime.now().isoformat() + "Z",
            "graded_by": "teacher001"
        }
        
        return {"status": 200, "data": grade}
    
    def update_mock_task(self, task_id, data):
        """更新模拟任务"""
        task = {
            "id": int(task_id),
            "title": data.get('title', '更新后的任务'),
            "description": data.get('description', ''),
            "updated_at": datetime.now().isoformat() + "Z"
        }
        
        return {"status": 200, "data": task}
    
    def log_message(self, format, *args):
        """重写日志方法以减少输出"""
        print(f"{self.address_string()} - {format % args}")

def run_mock_server(port=8080):
    """运行模拟服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, MockAPIHandler)
    print(f"Mock API server running on http://localhost:{port}")
    print("Press Ctrl+C to stop the server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nShutting down the server...")
        httpd.shutdown()

if __name__ == '__main__':
    run_mock_server()
